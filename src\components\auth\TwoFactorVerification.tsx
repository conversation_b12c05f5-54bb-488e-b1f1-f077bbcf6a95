
import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { useToast } from '@/hooks/use-toast';

const TwoFactorVerification = () => {
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  const userEmail = location.state?.email || '<EMAIL>';
  const userRole = location.state?.role || 'Admin';

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    if (otp.length !== 6) {
      toast({
        title: "Error",
        description: "Please enter a valid 6-digit code",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    // Simulate verification
    setTimeout(() => {
      if (otp === '123456') { // Demo code
        toast({
          title: "Verification successful!",
          description: `Welcome to Dr. Kumar Laboratories Admin Panel`,
        });
        navigate('/dashboard');
      } else {
        toast({
          title: "Invalid code",
          description: "Please check your code and try again",
          variant: "destructive",
        });
      }
      setIsLoading(false);
    }, 1000);
  };

  const handleResendCode = () => {
    toast({
      title: "Code resent",
      description: "A new verification code has been sent to your email",
    });
  };

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Form */}
      <div className="flex-1 flex items-center justify-center p-8 bg-gray-50">
        <div className="w-full max-w-md space-y-6">
          <div className="flex items-center gap-2 text-gray-600 mb-8">
            <ArrowLeft className="w-4 h-4" />
            <Link to="/" className="text-sm hover:text-gray-800">Back to dashboard</Link>
          </div>

          {/* Logo and Brand */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <img 
                src="/lovable-uploads/079f933a-4d0a-4e30-bf46-582b2b7fd97b.png" 
                alt="Dr. Kumar Laboratories"
                className="w-20 h-20 object-contain"
              />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Dr. Kumar Laboratories</h1>
          </div>

          <div className="space-y-2">
            <h2 className="text-3xl font-bold text-gray-900">Two Step Verification</h2>
            <p className="text-gray-600">
              A verification code has been sent to your mobile. Please enter it in the field below.
            </p>
          </div>

          <form onSubmit={handleVerification} className="space-y-6">
            <div className="space-y-4">
              <p className="text-sm text-gray-700 font-medium">
                Type your 6 digits security code
              </p>
              
              <div className="flex justify-center">
                <InputOTP 
                  maxLength={6} 
                  value={otp} 
                  onChange={(value) => setOtp(value)}
                >
                  <InputOTPGroup>
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                    <InputOTPSlot index={4} />
                    <InputOTPSlot index={5} />
                  </InputOTPGroup>
                </InputOTP>
              </div>
            </div>

            <Button type="submit" className="w-full h-12 bg-[#FFD700] hover:bg-[#E6C200] text-black" disabled={isLoading}>
              {isLoading ? "Verifying..." : "Verify My Account"}
            </Button>
          </form>

          <p className="text-center text-sm text-gray-600">
            Didn't get the code?{' '}
            <button 
              onClick={handleResendCode}
              className="text-[#FFD700] hover:underline font-medium"
            >
              Resend
            </button>
          </p>
        </div>
      </div>

      {/* Right Side - Background Image */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <img 
          src="/lovable-uploads/********-1ce5-4e40-ad4e-f2133eca9b94.png"
          alt="Doctor Consultation"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/20"></div>
      </div>
    </div>
  );
};

export default TwoFactorVerification;
