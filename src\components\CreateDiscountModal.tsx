import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { X } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface CreateDiscountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (discountData: any) => void;
}

const CreateDiscountModal = ({
  isOpen,
  onClose,
  onSave,
}: CreateDiscountModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    productName: "",
    description: "",
    category: "",
    discountType: "Percentage",
    discountPercentage: "",
    minimumOrderAmount: "",
    maximumDiscount: "",
    usageLimit: "",
    validFrom: "",
    validTo: "",
    status: "Active",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.productName ||
      !formData.description ||
      !formData.discountPercentage
    ) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    onSave({
      ...formData,
      code: `${formData.productName
        .replace(/\s+/g, "")
        .toUpperCase()}${Math.floor(Math.random() * 1000)}`,
      type: formData.discountType,
      value: parseInt(formData.discountPercentage),
      minOrder: parseInt(formData.minimumOrderAmount) || 0,
      maxDiscount: parseInt(formData.maximumDiscount) || 0,
      usageLimit: parseInt(formData.usageLimit) || 0,
      used: 0,
      id: Date.now(),
    });

    onClose();
    setFormData({
      productName: "",
      description: "",
      category: "",
      discountType: "Percentage",
      discountPercentage: "",
      minimumOrderAmount: "",
      maximumDiscount: "",
      usageLimit: "",
      validFrom: "",
      validTo: "",
      status: "Active",
    });

    toast({
      title: "Discount Created",
      description: "New discount has been successfully created",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-full max-h-full w-screen h-screen bg-white dark:bg-gray-800 border-0 rounded-none p-0">
        <div className="flex flex-col h-full">
          <DialogHeader className="flex flex-row items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-white">
              Create Discount
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto p-6">
            <form
              id="discount-form"
              onSubmit={handleSubmit}
              className="max-w-4xl mx-auto space-y-6"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Product Name */}
                <div>
                  <Label
                    htmlFor="productName"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Product Name *
                  </Label>
                  <Input
                    id="productName"
                    value={formData.productName}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        productName: e.target.value,
                      }))
                    }
                    placeholder="Enter product name"
                    className="text-base px-4 py-2"
                    required
                  />
                </div>

                {/* Description */}
                <div>
                  <Label
                    htmlFor="description"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Description *
                  </Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    placeholder="Enter description"
                    className="text-base px-4 py-2"
                    required
                  />
                </div>

                {/* Category */}
                <div>
                  <Label
                    htmlFor="category"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Category *
                  </Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, category: value }))
                    }
                  >
                    <SelectTrigger className="text-base px-4 py-2">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Health Drinks">
                        Health Drinks
                      </SelectItem>
                      <SelectItem value="Supplements">Supplements</SelectItem>
                      <SelectItem value="Combo Packs">Combo Packs</SelectItem>
                      <SelectItem value="Ayurvedic Medicine">
                        Ayurvedic Medicine
                      </SelectItem>
                      <SelectItem value="Personal Care">
                        Personal Care
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Discount Type */}
                <div>
                  <Label
                    htmlFor="discountType"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Discount Type *
                  </Label>
                  <Select
                    value={formData.discountType}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, discountType: value }))
                    }
                  >
                    <SelectTrigger className="text-base px-4 py-2">
                      <SelectValue placeholder="Select discount type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Percentage">Percentage</SelectItem>
                      <SelectItem value="Fixed">Fixed Amount</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Discount Percentage */}
                <div>
                  <Label
                    htmlFor="discountPercentage"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Discount Percentage *
                  </Label>
                  <Input
                    id="discountPercentage"
                    type="number"
                    value={formData.discountPercentage}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        discountPercentage: e.target.value,
                      }))
                    }
                    placeholder="Enter percentage"
                    className="text-base px-4 py-2"
                    required
                  />
                </div>

                {/* Minimum Order Amount */}
                <div>
                  <Label
                    htmlFor="minimumOrderAmount"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Minimum Order Amount
                  </Label>
                  <Input
                    id="minimumOrderAmount"
                    type="number"
                    value={formData.minimumOrderAmount}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        minimumOrderAmount: e.target.value,
                      }))
                    }
                    placeholder="Enter minimum order amount"
                    className="text-base px-4 py-2"
                  />
                </div>

                {/* Maximum Discount */}
                <div>
                  <Label
                    htmlFor="maximumDiscount"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Maximum Discount
                  </Label>
                  <Input
                    id="maximumDiscount"
                    type="number"
                    value={formData.maximumDiscount}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        maximumDiscount: e.target.value,
                      }))
                    }
                    placeholder="Enter maximum discount amount"
                    className="text-base px-4 py-2"
                  />
                </div>

                {/* Usage Limit */}
                <div>
                  <Label
                    htmlFor="usageLimit"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Usage Limit
                  </Label>
                  <Input
                    id="usageLimit"
                    type="number"
                    value={formData.usageLimit}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        usageLimit: e.target.value,
                      }))
                    }
                    placeholder="Enter usage limit"
                    className="text-base px-4 py-2"
                  />
                </div>

                {/* Valid From */}
                <div>
                  <Label
                    htmlFor="validFrom"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Valid From
                  </Label>
                  <Input
                    id="validFrom"
                    type="date"
                    value={formData.validFrom}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        validFrom: e.target.value,
                      }))
                    }
                    className="text-base px-4 py-2"
                  />
                </div>

                {/* Valid To */}
                <div>
                  <Label
                    htmlFor="validTo"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Valid To
                  </Label>
                  <Input
                    id="validTo"
                    type="date"
                    value={formData.validTo}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        validTo: e.target.value,
                      }))
                    }
                    className="text-base px-4 py-2"
                  />
                </div>

                {/* Status */}
                <div>
                  <Label
                    htmlFor="status"
                    className="text-base font-medium text-gray-700 dark:text-gray-300"
                  >
                    Status
                  </Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, status: value }))
                    }
                  >
                    <SelectTrigger className="text-base px-4 py-2">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </form>
          </div>

          {/* Footer with buttons */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-6">
            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="px-6 py-2"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                form="discount-form"
                className="bg-[#FFD700] hover:bg-[#E6C200] text-black px-6 py-2"
                onClick={(e) => {
                  e.preventDefault();
                  const form = document.getElementById(
                    "discount-form"
                  ) as HTMLFormElement;
                  if (form) {
                    form.requestSubmit();
                  }
                }}
              >
                Create Discount
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateDiscountModal;
