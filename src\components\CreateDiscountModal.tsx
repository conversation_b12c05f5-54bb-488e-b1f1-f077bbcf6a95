
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Percent } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface CreateDiscountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (discountData: any) => void;
}

const CreateDiscountModal = ({ isOpen, onClose, onSave }: CreateDiscountModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    type: "Percentage",
    value: "",
    applicableOn: "All Products",
    category: "",
    minimumQuantity: "",
    validFrom: "",
    validTo: "",
    status: "Active"
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.description || !formData.value) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    onSave({
      ...formData,
      value: parseInt(formData.value),
      minimumQuantity: parseInt(formData.minimumQuantity),
      id: Date.now()
    });
    
    onClose();
    setFormData({
      name: "",
      description: "",
      type: "Percentage",
      value: "",
      applicableOn: "All Products",
      category: "",
      minimumQuantity: "",
      validFrom: "",
      validTo: "",
      status: "Active"
    });

    toast({
      title: "Discount Created",
      description: "New discount has been successfully created",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-gray-900 dark:text-white flex items-center gap-2">
            <Percent className="w-5 h-5 text-blue-600" />
            Create New Discount
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <Label htmlFor="name" className="text-gray-700 dark:text-gray-300">
                Discount Name *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter discount name"
                className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
                required
              />
            </div>

            <div className="col-span-2">
              <Label htmlFor="description" className="text-gray-700 dark:text-gray-300">
                Description *
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter discount description"
                className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
                required
              />
            </div>

            <div>
              <Label htmlFor="type" className="text-gray-700 dark:text-gray-300">
                Discount Type *
              </Label>
              <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
                <SelectTrigger className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Percentage">Percentage</SelectItem>
                  <SelectItem value="Fixed">Fixed Amount</SelectItem>
                  <SelectItem value="Buy One Get One">BOGO</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="value" className="text-gray-700 dark:text-gray-300">
                Discount Value *
              </Label>
              <Input
                id="value"
                type="number"
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                placeholder={formData.type === "Percentage" ? "Enter percentage" : "Enter amount"}
                className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
                required
              />
            </div>

            <div>
              <Label htmlFor="applicableOn" className="text-gray-700 dark:text-gray-300">
                Applicable On
              </Label>
              <Select value={formData.applicableOn} onValueChange={(value) => setFormData(prev => ({ ...prev, applicableOn: value }))}>
                <SelectTrigger className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All Products">All Products</SelectItem>
                  <SelectItem value="Specific Category">Specific Category</SelectItem>
                  <SelectItem value="Specific Products">Specific Products</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="category" className="text-gray-700 dark:text-gray-300">
                Category (if applicable)
              </Label>
              <Input
                id="category"
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                placeholder="Enter category"
                className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              />
            </div>

            <div>
              <Label htmlFor="minimumQuantity" className="text-gray-700 dark:text-gray-300">
                Minimum Quantity
              </Label>
              <Input
                id="minimumQuantity"
                type="number"
                value={formData.minimumQuantity}
                onChange={(e) => setFormData(prev => ({ ...prev, minimumQuantity: e.target.value }))}
                placeholder="Enter minimum quantity"
                className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              />
            </div>

            <div>
              <Label htmlFor="validFrom" className="text-gray-700 dark:text-gray-300">
                Valid From
              </Label>
              <Input
                id="validFrom"
                type="date"
                value={formData.validFrom}
                onChange={(e) => setFormData(prev => ({ ...prev, validFrom: e.target.value }))}
                className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              />
            </div>

            <div>
              <Label htmlFor="validTo" className="text-gray-700 dark:text-gray-300">
                Valid To
              </Label>
              <Input
                id="validTo"
                type="date"
                value={formData.validTo}
                onChange={(e) => setFormData(prev => ({ ...prev, validTo: e.target.value }))}
                className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              />
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700 text-white">
              Create Discount
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateDiscountModal;
