
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

interface CreateCouponModalProps {
  isOpen: boolean;
  onClose: () => void;
  coupon?: any;
  onSave: (couponData: any) => void;
}

const CreateCouponModal = ({ isOpen, onClose, coupon, onSave }: CreateCouponModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    code: '',
    description: '',
    productName: '',
    category: '',
    type: 'Percentage',
    value: '',
    minOrder: '',
    maxDiscount: '',
    usageLimit: '',
    validFrom: '',
    validTo: '',
    status: 'Active'
  });

  useEffect(() => {
    if (coupon) {
      setFormData({
        code: coupon.code || '',
        description: coupon.description || '',
        productName: coupon.productName || '',
        category: coupon.category || '',
        type: coupon.type || 'Percentage',
        value: coupon.value?.toString() || '',
        minOrder: coupon.minOrder?.toString() || '',
        maxDiscount: coupon.maxDiscount?.toString() || '',
        usageLimit: coupon.usageLimit?.toString() || '',
        validFrom: coupon.validFrom || '',
        validTo: coupon.validTo || '',
        status: coupon.status || 'Active'
      });
    } else {
      setFormData({
        code: '',
        description: '',
        productName: '',
        category: '',
        type: 'Percentage',
        value: '',
        minOrder: '',
        maxDiscount: '',
        usageLimit: '',
        validFrom: '',
        validTo: '',
        status: 'Active'
      });
    }
  }, [coupon]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const couponData = {
      ...formData,
      value: parseFloat(formData.value),
      minOrder: parseFloat(formData.minOrder),
      maxDiscount: parseFloat(formData.maxDiscount),
      usageLimit: parseInt(formData.usageLimit)
    };

    onSave(couponData);
    onClose();
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-2xl text-gray-900 dark:text-white">
            {coupon ? 'Edit Coupon' : 'Create New Coupon'}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="code" className="text-base">Coupon Code</Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => handleInputChange('code', e.target.value)}
                placeholder="Enter coupon code"
                className="text-base px-4 py-2"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="productName" className="text-base">Product Name</Label>
              <Input
                id="productName"
                value={formData.productName}
                onChange={(e) => handleInputChange('productName', e.target.value)}
                placeholder="Enter product name"
                className="text-base px-4 py-2"
                required
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="description" className="text-base">Description</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter coupon description"
                className="text-base px-4 py-2"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category" className="text-base">Category</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger className="text-base px-4 py-2">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Health Drinks">Health Drinks</SelectItem>
                  <SelectItem value="Supplements">Supplements</SelectItem>
                  <SelectItem value="Combo Packs">Combo Packs</SelectItem>
                  <SelectItem value="Ayurvedic Products">Ayurvedic Products</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type" className="text-base">Discount Type</Label>
              <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                <SelectTrigger className="text-base px-4 py-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Percentage">Percentage</SelectItem>
                  <SelectItem value="Fixed">Fixed Amount</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="value" className="text-base">
                {formData.type === 'Percentage' ? 'Discount Percentage' : 'Discount Amount'}
              </Label>
              <Input
                id="value"
                type="number"
                value={formData.value}
                onChange={(e) => handleInputChange('value', e.target.value)}
                placeholder={formData.type === 'Percentage' ? 'Enter percentage' : 'Enter amount'}
                className="text-base px-4 py-2"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="minOrder" className="text-base">Minimum Order Amount</Label>
              <Input
                id="minOrder"
                type="number"
                value={formData.minOrder}
                onChange={(e) => handleInputChange('minOrder', e.target.value)}
                placeholder="Enter minimum order amount"
                className="text-base px-4 py-2"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxDiscount" className="text-base">Maximum Discount</Label>
              <Input
                id="maxDiscount"
                type="number"
                value={formData.maxDiscount}
                onChange={(e) => handleInputChange('maxDiscount', e.target.value)}
                placeholder="Enter maximum discount"
                className="text-base px-4 py-2"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="usageLimit" className="text-base">Usage Limit</Label>
              <Input
                id="usageLimit"
                type="number"
                value={formData.usageLimit}
                onChange={(e) => handleInputChange('usageLimit', e.target.value)}
                placeholder="Enter usage limit"
                className="text-base px-4 py-2"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="validFrom" className="text-base">Valid From</Label>
              <Input
                id="validFrom"
                type="date"
                value={formData.validFrom}
                onChange={(e) => handleInputChange('validFrom', e.target.value)}
                className="text-base px-4 py-2"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="validTo" className="text-base">Valid To</Label>
              <Input
                id="validTo"
                type="date"
                value={formData.validTo}
                onChange={(e) => handleInputChange('validTo', e.target.value)}
                className="text-base px-4 py-2"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status" className="text-base">Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger className="text-base px-4 py-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="text-base px-6 py-2"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-[#FFD700] hover:bg-[#E6C200] text-black text-base px-6 py-2"
            >
              {coupon ? 'Update Coupon' : 'Create Coupon'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateCouponModal;
