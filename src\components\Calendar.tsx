import React, { useState, useRef } from "react";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Plus, CalendarDays, Image, X, Upload, Eye } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import ImagePreviewModal from "./ImagePreviewModal";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface BannerEvent {
  id: string;
  title: string;
  description: string;
  date: Date;
  imageUrl?: string;
  priority: "Level 1" | "Level 2" | "Level 3";
}

const Calendar = () => {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [events, setEvents] = useState<BannerEvent[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string>("");
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [bannerForm, setBannerForm] = useState({
    title: "",
    description: "",
    imageUrl: "",
    priority: "Level 2" as "Level 1" | "Level 2" | "Level 3",
  });
  const { toast } = useToast();

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const result = event.target?.result as string;
        setImagePreview(result);
        setBannerForm({ ...bannerForm, imageUrl: result });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setBannerForm({ ...bannerForm, imageUrl: url });

    if (url) {
      setImagePreview(url);
    } else {
      setImagePreview(null);
    }
  };

  const handleCreateBanner = (e: React.FormEvent) => {
    e.preventDefault();

    if (!date) {
      toast({
        title: "Error",
        description: "Please select a date for the banner",
        variant: "destructive",
      });
      return;
    }

    const newEvent: BannerEvent = {
      id: Date.now().toString(),
      title: bannerForm.title,
      description: bannerForm.description,
      date: date,
      imageUrl: bannerForm.imageUrl,
      priority: bannerForm.priority,
    };

    setEvents([...events, newEvent]);
    setBannerForm({
      title: "",
      description: "",
      imageUrl: "",
      priority: "Level 2",
    });
    setImagePreview(null);
    setIsModalOpen(false);

    toast({
      title: "Banner created!",
      description: `Banner scheduled with ${bannerForm.priority} priority`,
    });
  };

  const removeImagePreview = () => {
    setImagePreview(null);
    setBannerForm({ ...bannerForm, imageUrl: "" });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const openImagePreview = (imageUrl: string) => {
    setPreviewImage(imageUrl);
    setIsPreviewOpen(true);
  };

  const getEventsForDate = (selectedDate: Date) => {
    return events.filter(
      (event) => event.date.toDateString() === selectedDate.toDateString()
    );
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "Level 1":
        return "bg-red-100 text-red-800 border-red-200";
      case "Level 2":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Level 3":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Calendar
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Manage banners and promotional events
          </p>
        </div>

        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogTrigger asChild>
            <Button className="bg-blue-600 hover:bg-blue-700 transition-all duration-300 ease-in-out">
              <Plus className="w-4 h-4 mr-2" />
              Create Banner
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Banner</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreateBanner} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Banner Title</Label>
                <Input
                  id="title"
                  value={bannerForm.title}
                  onChange={(e) =>
                    setBannerForm({ ...bannerForm, title: e.target.value })
                  }
                  placeholder="Enter banner title"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={bannerForm.description}
                  onChange={(e) =>
                    setBannerForm({
                      ...bannerForm,
                      description: e.target.value,
                    })
                  }
                  placeholder="Enter banner description"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label>Banner Image</Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="flex items-center gap-2 transition-all duration-300 ease-in-out"
                  >
                    <Upload className="w-4 h-4" />
                    Upload Image
                  </Button>
                  <Input
                    placeholder="Or paste image URL"
                    value={bannerForm.imageUrl}
                    onChange={handleImageUrlChange}
                    className="flex-1"
                  />
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </div>

              {/* Image Preview Section */}
              {imagePreview && (
                <div className="space-y-2">
                  <Label>Image Preview</Label>
                  <div className="relative border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-800 transition-all duration-300 ease-in-out">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-green-600 dark:text-green-400 flex items-center gap-2">
                        <Image className="w-4 h-4" />
                        Image Attached
                      </span>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => openImagePreview(imagePreview)}
                          className="transition-all duration-300 ease-in-out"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={removeImagePreview}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-300 ease-in-out"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center overflow-hidden">
                      <img
                        src={imagePreview}
                        alt="Banner preview"
                        className="max-w-full max-h-full object-contain"
                        onError={() => {
                          toast({
                            title: "Invalid Image",
                            description: "Unable to load image",
                            variant: "destructive",
                          });
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="priority">Banner Priority Level</Label>
                <Select
                  value={bannerForm.priority}
                  onValueChange={(value: "Level 1" | "Level 2" | "Level 3") =>
                    setBannerForm({ ...bannerForm, priority: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Level 1">
                      Level 1 - High Priority
                    </SelectItem>
                    <SelectItem value="Level 2">
                      Level 2 - Medium Priority
                    </SelectItem>
                    <SelectItem value="Level 3">
                      Level 3 - Low Priority
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsModalOpen(false);
                    setImagePreview(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 transition-all duration-300 ease-in-out"
                >
                  Create Banner
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarDays className="w-5 h-5" />
              Calendar View
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CalendarComponent
              mode="single"
              selected={date}
              onSelect={setDate}
              className="rounded-md border w-full"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Events for {date?.toLocaleDateString()}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {date && getEventsForDate(date).length > 0 ? (
                getEventsForDate(date).map((event) => (
                  <div
                    key={event.id}
                    className="p-3 border rounded-lg transition-all duration-300 ease-in-out hover:shadow-md"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {event.title}
                      </h4>
                      <span
                        className={`text-xs px-2 py-1 rounded-full border ${getPriorityColor(
                          event.priority
                        )}`}
                      >
                        {event.priority}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                      {event.description}
                    </p>
                    {event.imageUrl && (
                      <button
                        onClick={() => openImagePreview(event.imageUrl!)}
                        className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-700 transition-colors duration-300 ease-in-out"
                      >
                        <Image className="w-3 h-3" />
                        Image Attached
                      </button>
                    )}
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-sm">
                  No banners scheduled for this date
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Scheduled Banners</CardTitle>
        </CardHeader>
        <CardContent>
          {events.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {events.map((event) => (
                <div
                  key={event.id}
                  className="border rounded-lg p-4 transition-all duration-300 ease-in-out hover:shadow-md"
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      {event.title}
                    </h3>
                    <span
                      className={`text-xs px-2 py-1 rounded-full border ${getPriorityColor(
                        event.priority
                      )}`}
                    >
                      {event.priority}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                    {event.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Scheduled: {event.date.toLocaleDateString()}
                    </div>
                    {event.imageUrl && (
                      <button
                        onClick={() => openImagePreview(event.imageUrl!)}
                        className="text-xs text-blue-600 hover:text-blue-700 transition-colors duration-300 ease-in-out flex items-center gap-1"
                      >
                        <Eye className="w-3 h-3" />
                        Preview
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">
              No banners created yet. Click "Create Banner" to get started.
            </p>
          )}
        </CardContent>
      </Card>

      <ImagePreviewModal
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        imageUrl={previewImage}
        title="Banner Image Preview"
      />
    </div>
  );
};

export default Calendar;
