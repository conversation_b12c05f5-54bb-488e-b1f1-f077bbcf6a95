import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

const feedbackStats = [
  { title: "Total Feedback", value: "5", color: "text-gray-900" },
  { title: "Pending", value: "2", color: "text-orange-600" },
  { title: "In Progress", value: "2", color: "text-blue-600" },
  { title: "Completed", value: "1", color: "text-green-600" },
];

const feedbackData = [
  {
    id: 1,
    customer: "<PERSON><PERSON>",
    email: "r<PERSON><PERSON>@email.com",
    type: "Complaint",
    subject: "Product quality issue",
    message: "The Aloe Vera juice had an unusual taste",
    priority: "High",
    progress: "In Progress",
    date: "2024-01-20",
  },
  {
    id: 2,
    customer: "Priya <PERSON>",
    email: "<EMAIL>",
    type: "Feedback",
    subject: "Great product experience",
    message: "Very satisfied with the Ashwagandha capsules",
    priority: "Medium",
    progress: "Completed",
    date: "2024-01-19",
  },
  {
    id: 3,
    customer: "Amit Patel",
    email: "<EMAIL>",
    type: "Complaint",
    subject: "Delivery delay",
    message: "Order was delivered 3 days late",
    priority: "Medium",
    progress: "Pending",
    date: "2024-01-18",
  },
  {
    id: 4,
    customer: "Sneha Reddy",
    email: "<EMAIL>",
    type: "Suggestion",
    subject: "New product request",
    message: "Please consider adding turmeric supplements",
    priority: "Low",
    progress: "Initiated",
    date: "2024-01-17",
  },
  {
    id: 5,
    customer: "Vikram Singh",
    email: "<EMAIL>",
    type: "Complaint",
    subject: "Website navigation issue",
    message: "Difficulty finding product categories",
    priority: "Medium",
    progress: "Pending",
    date: "2024-01-16",
  },
];

const ComplaintsFeedback = () => {
  const { toast } = useToast();
  const [feedback, setFeedback] = useState(feedbackData);

  const handleProgressChange = (id: number, newProgress: string) => {
    setFeedback((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, progress: newProgress } : item
      )
    );

    toast({
      title: "Status Updated",
      description: `Feedback status changed to ${newProgress}`,
    });
  };

  const handlePriorityChange = (id: number, newPriority: string) => {
    setFeedback((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, priority: newPriority } : item
      )
    );

    toast({
      title: "Priority Updated",
      description: `Priority changed to ${newPriority}`,
    });
  };

  const getProgressColor = (progress: string) => {
    switch (progress) {
      case "Initiated":
        return "bg-gray-100 text-gray-700";
      case "In Progress":
        return "bg-blue-100 text-blue-700";
      case "Completed":
        return "bg-green-100 text-green-700";
      case "Pending":
        return "bg-orange-100 text-orange-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-700 border-red-200";
      case "Medium":
        return "bg-orange-100 text-orange-700 border-orange-200";
      case "Low":
        return "bg-green-100 text-green-700 border-green-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Feedback
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Manage customer feedback and resolve complaints
          </p>
        </div>
      </div>

      {/* Feedback Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {feedbackStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={stat.value}
                  className="text-4xl font-bold"
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Feedback Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Customer Feedback
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Customer
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Type
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Subject
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Priority
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Progress
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Date
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {feedback.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <div className="flex flex-col">
                      <AnimatedText className="font-medium">
                        {item.customer}
                      </AnimatedText>
                      <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                        {item.email}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{item.type}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {item.subject}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate transition-colors duration-500 ease-in-out">
                        {item.message}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Select
                      value={item.priority}
                      onValueChange={(value) =>
                        handlePriorityChange(item.id, value)
                      }
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue>
                          <Badge
                            variant="secondary"
                            className={`${getPriorityColor(
                              item.priority
                            )} border transition-all duration-300 ease-in-out`}
                          >
                            {item.priority}
                          </Badge>
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="High">High</SelectItem>
                        <SelectItem value="Medium">Medium</SelectItem>
                        <SelectItem value="Low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Select
                      value={item.progress}
                      onValueChange={(value) =>
                        handleProgressChange(item.id, value)
                      }
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue>
                          <Badge
                            variant="secondary"
                            className={getProgressColor(item.progress)}
                          >
                            {item.progress}
                          </Badge>
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Initiated">Initiated</SelectItem>
                        <SelectItem value="In Progress">In Progress</SelectItem>
                        <SelectItem value="Completed">Completed</SelectItem>
                        <SelectItem value="Pending">Pending</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell className="text-sm text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                    {item.date}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default ComplaintsFeedback;
