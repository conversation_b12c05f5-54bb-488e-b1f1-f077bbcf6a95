
import { Home, Package, ShoppingCart, Users, Archive, BarChart3, Ticket, RotateCcw, Users2, MessageSquare, Calendar } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
} from "@/components/ui/sidebar";

const menuItems = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: Home,
  },
  {
    title: "Products",
    url: "/products",
    icon: Package,
  },
  {
    title: "Orders",
    url: "/orders",
    icon: ShoppingCart,
  },
  {
    title: "Customers",
    url: "/customers",
    icon: Users,
  },
  {
    title: "Inventory",
    url: "/inventory",
    icon: Archive,
  },
  {
    title: "Coupons",
    url: "/coupons",
    icon: Ticket,
  },
];

const businessTools = [
  {
    title: "Analytics",
    url: "/analytics",
    icon: BarChart3,
  },
  {
    title: "Calendar",
    url: "/calendar", 
    icon: Calendar,
  },
  {
    title: "Feedback",
    url: "/feedback",
    icon: MessageSquare,
  },
  {
    title: "Returns",
    url: "/returns",
    icon: RotateCcw,
  },
];

const otherItems = [
  {
    title: "Staff",
    url: "/staff",
    icon: Users2,
  },
];

export function AppSidebar() {
  const location = useLocation();
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Check theme on component mount and listen for theme changes
  useEffect(() => {
    const checkTheme = () => {
      const isDark = document.documentElement.classList.contains('dark');
      setIsDarkMode(isDark);
    };

    checkTheme();
    
    // Create a MutationObserver to watch for theme changes
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);

  return (
    <Sidebar className="border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 transition-colors duration-500 ease-in-out">
      <SidebarHeader className="p-6 border-b border-gray-200 dark:border-gray-700 transition-colors duration-500 ease-in-out">
        <Link to="/dashboard" className="flex items-center gap-3 hover:opacity-80 transition-opacity duration-200">
          <div className="relative w-10 h-10 rounded-lg overflow-hidden transition-opacity duration-500 ease-in-out">
            {/* Light mode logo */}
            <img
              src="/lovable-uploads/73a3c9d3-4dc2-424c-af2e-8e092b3e5101.png"
              alt="Dr. Kumar Laboratories"
              className={`absolute inset-0 w-full h-full object-contain transition-opacity duration-500 ease-in-out ${
                isDarkMode ? 'opacity-0' : 'opacity-100'
              }`}
            />
            {/* Dark mode logo */}
            <img
              src="/lovable-uploads/0df92a85-211b-467d-99a6-583490e9ebba.png"
              alt="Dr. Kumar Laboratories"
              className={`absolute inset-0 w-full h-full object-contain transition-opacity duration-500 ease-in-out ${
                isDarkMode ? 'opacity-100' : 'opacity-0'
              }`}
            />
          </div>
          <div>
            <h1 className="font-semibold text-base text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Dr. Kumar Laboratories</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">Admin Dashboard</p>
          </div>
        </Link>
      </SidebarHeader>
      
      <SidebarContent className="p-4">
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2 transition-colors duration-500 ease-in-out">
            Main Menu
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild 
                    className={`${location.pathname === item.url ? 'bg-[#FFD700]/20 text-[#FFD700] border-l-4 border-[#FFD700]' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'} transition-colors duration-300 ease-in-out text-base`}
                  >
                    <Link to={item.url} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <item.icon className="w-5 h-5" />
                        <span className="font-medium">{item.title}</span>
                      </div>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2 transition-colors duration-500 ease-in-out">
            Business Tools
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {businessTools.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild 
                    className={`${location.pathname === item.url ? 'bg-[#FFD700]/20 text-[#FFD700] border-l-4 border-[#FFD700]' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'} transition-colors duration-300 ease-in-out text-base`}
                  >
                    <Link to={item.url} className="flex items-center gap-3">
                      <item.icon className="w-5 h-5" />
                      <span className="font-medium">{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {otherItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    asChild 
                    className={`${location.pathname === item.url ? 'bg-[#FFD700]/20 text-[#FFD700] border-l-4 border-[#FFD700]' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'} transition-colors duration-300 ease-in-out text-base`}
                  >
                    <Link to={item.url} className="flex items-center gap-3">
                      <item.icon className="w-5 h-5" />
                      <span className="font-medium">{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
