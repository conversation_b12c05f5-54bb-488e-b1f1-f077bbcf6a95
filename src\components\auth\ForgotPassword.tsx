
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate sending reset email
    setTimeout(() => {
      toast({
        title: "Reset link sent!",
        description: "Please check your email for password reset instructions",
      });
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Form */}
      <div className="flex-1 flex items-center justify-center p-8 bg-gray-50">
        <div className="w-full max-w-md space-y-6">
          <div className="flex items-center gap-2 text-gray-600 mb-8">
            <ArrowLeft className="w-4 h-4" />
            <Link to="/" className="text-sm hover:text-gray-800">Back to dashboard</Link>
          </div>

          {/* Logo and Brand */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <img 
                src="/lovable-uploads/079f933a-4d0a-4e30-bf46-582b2b7fd97b.png" 
                alt="Dr. Kumar Laboratories"
                className="w-20 h-20 object-contain"
              />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Dr. Kumar Laboratories</h1>
          </div>

          <div className="space-y-2">
            <h2 className="text-3xl font-bold text-gray-900">Forgot Your Password?</h2>
            <p className="text-gray-600">
              Enter the email address linked to your account, and we'll send you a link to reset your password.
            </p>
          </div>

          <form onSubmit={handleResetPassword} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email<span className="text-red-500">*</span></Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                required
                className="h-12"
              />
            </div>

            <Button type="submit" className="w-full h-12 bg-[#FFD700] hover:bg-[#E6C200] text-black" disabled={isLoading}>
              {isLoading ? "Sending Reset Link..." : "Send Reset Link"}
            </Button>
          </form>

          <p className="text-center text-sm text-gray-600">
            Wait, I remember my password...{' '}
            <Link to="/auth/signin" className="text-[#FFD700] hover:underline font-medium">
              Click here
            </Link>
          </p>
        </div>
      </div>

      {/* Right Side - Background Image */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <img 
          src="/lovable-uploads/55118515-1ce5-4e40-ad4e-f2133eca9b94.png"
          alt="Doctor Consultation"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/20"></div>
      </div>
    </div>
  );
};

export default ForgotPassword;
