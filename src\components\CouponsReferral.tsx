import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Plus, Edit, Trash2, Co<PERSON>, <PERSON>rkles } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import CreateCouponModal from "./CreateCouponModal";
import CreateDiscountModal from "./CreateDiscountModal";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

const couponStats = [
  { title: "Total Coupons", value: "3", color: "text-gray-900" },
  { title: "Active", value: "2", color: "text-[#FFD700]" },
  { title: "Inactive", value: "1", color: "text-red-600" },
  { title: "Total Savings", value: "₹12,450", color: "text-blue-600" },
];

const couponsData = [
  {
    id: 1,
    code: "WELCOME10",
    description: "Welcome discount for new customers",
    type: "Percentage",
    value: 10,
    minOrder: 500,
    maxDiscount: 100,
    usageLimit: 1000,
    used: 45,
    status: "Active",
    validFrom: "2024-01-01",
    validTo: "2024-12-31",
    productName: "Kapiva Aloe Vera Juice",
    category: "Health Drinks",
  },
  {
    id: 2,
    code: "HEALTH50",
    description: "Flat discount on health products",
    type: "Fixed",
    value: 50,
    minOrder: 300,
    maxDiscount: 50,
    usageLimit: 500,
    used: 123,
    status: "Active",
    validFrom: "2024-01-15",
    validTo: "2024-06-30",
    productName: "Ashwagandha Capsules",
    category: "Supplements",
  },
  {
    id: 3,
    code: "EXPIRED20",
    description: "Expired discount code",
    type: "Percentage",
    value: 20,
    minOrder: 1000,
    maxDiscount: 200,
    usageLimit: 100,
    used: 89,
    status: "Inactive",
    validFrom: "2023-01-01",
    validTo: "2023-12-31",
    productName: "Health Combo Pack",
    category: "Combo Packs",
  },
];

const CouponsReferral = () => {
  const { toast } = useToast();
  const [coupons, setCoupons] = useState(couponsData);
  const [selectedCoupon, setSelectedCoupon] = useState(null);
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [isDiscountModalOpen, setIsDiscountModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("coupons");
  const [searchTerm, setSearchTerm] = useState("");

  const handleCreateCoupon = () => {
    setSelectedCoupon(null);
    setIsCouponModalOpen(true);
  };

  const handleCreateDiscount = () => {
    setSelectedCoupon(null);
    setIsDiscountModalOpen(true);
  };

  const handleEditCoupon = (coupon) => {
    setSelectedCoupon(coupon);
    setIsCouponModalOpen(true);
  };

  const handleDeleteCoupon = (couponId) => {
    setCoupons((prev) => prev.filter((c) => c.id !== couponId));
    toast({
      title: "Coupon Deleted",
      description: "Coupon has been successfully deleted",
    });
  };

  const handleDuplicateCoupon = (coupon) => {
    const duplicatedCoupon = {
      ...coupon,
      id: Date.now(),
      code: `${coupon.code}_COPY`,
      used: 0,
    };
    setCoupons((prev) => [...prev, duplicatedCoupon]);
    toast({
      title: "Coupon Duplicated",
      description: "Coupon has been successfully duplicated",
    });
  };

  const handleSaveCoupon = (couponData) => {
    if (selectedCoupon) {
      // Update existing coupon
      setCoupons((prev) =>
        prev.map((c) =>
          c.id === selectedCoupon.id
            ? { ...couponData, id: selectedCoupon.id }
            : c
        )
      );
      toast({
        title: "Coupon Updated",
        description: "Coupon has been successfully updated",
      });
    } else {
      // Add new coupon
      setCoupons((prev) => [
        ...prev,
        { ...couponData, id: Date.now(), used: 0 },
      ]);
      toast({
        title: "Coupon Created",
        description: "New coupon has been successfully created",
      });
    }
  };

  // Calculate dynamic stats
  const dynamicStats = [
    {
      title: "Total Coupons",
      value: coupons.length.toString(),
      color: "text-gray-900",
    },
    {
      title: "Active",
      value: coupons.filter((c) => c.status === "Active").length.toString(),
      color: "text-[#FFD700]",
    },
    {
      title: "Inactive",
      value: coupons.filter((c) => c.status === "Inactive").length.toString(),
      color: "text-red-600",
    },
    {
      title: "Total Savings",
      value: `₹${coupons
        .reduce((sum, c) => sum + c.used * c.value, 0)
        .toLocaleString()}`,
      color: "text-blue-600",
    },
  ];

  // Filter coupons based on search
  const filteredCoupons = coupons.filter(
    (coupon) =>
      coupon.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      coupon.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      coupon.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Coupons
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Manage discount coupons and referral programs
          </p>
        </div>
      </div>

      {/* Dynamic Coupon Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dynamicStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-base font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={stat.value}
                  className={`text-3xl font-bold ${stat.color}`}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filter */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search coupons by code, product, or category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="text-base px-4 py-2"
              />
            </div>
            <div className="flex gap-2">
              <Button
                className="bg-[#FFD700] hover:bg-[#E6C200] text-black transition-colors duration-300 ease-in-out px-6 py-3 text-base"
                onClick={handleCreateCoupon}
              >
                <Sparkles className="w-5 h-5 mr-2" />
                Create Coupon
              </Button>
              <Button
                className="bg-blue-600 hover:bg-blue-700 text-white transition-colors duration-300 ease-in-out px-6 py-3 text-base"
                onClick={handleCreateDiscount}
              >
                <Plus className="w-5 h-5 mr-2" />
                Create Discount
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Tabs for Create Coupon and Create Discount */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex items-center justify-between mb-4">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="coupons" className="text-base">
              Coupons
            </TabsTrigger>
            <TabsTrigger value="discounts" className="text-base">
              Discounts
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="coupons">
          <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                Coupons
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-base font-semibold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                      Coupon Code
                    </TableHead>
                    <TableHead className="text-base font-semibold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                      Discount Type & Value
                    </TableHead>
                    <TableHead className="text-base font-semibold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                      Status
                    </TableHead>
                    <TableHead className="text-base font-semibold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                      Valid Period
                    </TableHead>
                    <TableHead className="text-base font-semibold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                      Product Name
                    </TableHead>
                    <TableHead className="text-base font-semibold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                      Category
                    </TableHead>
                    <TableHead className="text-base font-semibold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCoupons.map((coupon) => (
                    <TableRow key={coupon.id}>
                      {/* Coupon Code */}
                      <TableCell className="px-4 py-2">
                        <div className="flex flex-col">
                          <AnimatedText className="font-medium text-base">
                            {coupon.code}
                          </AnimatedText>
                          <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                            {coupon.description}
                          </span>
                        </div>
                      </TableCell>

                      {/* Discount Type & Value */}
                      <TableCell className="px-4 py-2">
                        <div className="flex flex-col">
                          <Badge variant="outline" className="w-fit mb-1">
                            {coupon.type}
                          </Badge>
                          <span className="text-base text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                            {coupon.type === "Percentage"
                              ? `${coupon.value}%`
                              : `₹${coupon.value}`}
                          </span>
                        </div>
                      </TableCell>

                      {/* Status */}
                      <TableCell className="px-4 py-2">
                        <Badge
                          variant="secondary"
                          className={
                            coupon.status === "Active"
                              ? "bg-[#FFD700]/20 text-[#FFD700] border-[#FFD700]/30"
                              : "bg-red-100 text-red-700"
                          }
                        >
                          {coupon.status}
                        </Badge>
                      </TableCell>
                      {/* Valid Period */}
                      <TableCell className="px-4 py-2">
                        <div className="text-sm text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                          <div>{coupon.validFrom}</div>
                          <div className="text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                            to {coupon.validTo}
                          </div>
                        </div>
                      </TableCell>

                      {/* Product Name */}
                      <TableCell className="px-4 py-2">
                        <span className="font-medium text-base text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                          {coupon.productName}
                        </span>
                      </TableCell>

                      {/* Category */}
                      <TableCell className="px-4 py-2">
                        <Badge variant="outline" className="w-fit">
                          {coupon.category}
                        </Badge>
                      </TableCell>
                      <TableCell className="px-4 py-2">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditCoupon(coupon)}
                            className="transition-colors duration-300 ease-in-out"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDuplicateCoupon(coupon)}
                            className="transition-colors duration-300 ease-in-out"
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteCoupon(coupon.id)}
                            className="transition-colors duration-300 ease-in-out text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="discounts">
          <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                Active Discounts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-base text-gray-500 dark:text-gray-400">
                  No discounts available yet.
                </p>
                <Button
                  className="mt-4 bg-blue-600 hover:bg-blue-700 text-white text-base px-6 py-3"
                  onClick={handleCreateDiscount}
                >
                  Create Your First Discount
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <CreateCouponModal
        isOpen={isCouponModalOpen}
        onClose={() => setIsCouponModalOpen(false)}
        coupon={selectedCoupon}
        onSave={handleSaveCoupon}
      />

      <CreateDiscountModal
        isOpen={isDiscountModalOpen}
        onClose={() => setIsDiscountModalOpen(false)}
        onSave={handleSaveCoupon}
      />
    </div>
  );
};

export default CouponsReferral;
