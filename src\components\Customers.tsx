import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Eye, Download, RefreshCw, Search } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import CustomerViewModal from "./CustomerViewModal";
import SendEmailModal from "./SendEmailModal";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

const customerStats = [
  { title: "Total Customers", value: "2", color: "text-gray-900" },
  { title: "Active", value: "2", color: "text-green-600" },
  { title: "Inactive", value: "0", color: "text-red-600" },
  { title: "New This Month", value: "1", color: "text-blue-600" },
];

const customersData = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+91 9876543210",
    city: "Mumbai",
    status: "Active",
    totalOrders: 5,
    totalSpent: "₹2,497",
    lastOrder: "1/20/2024",
    joinDate: "12/15/2023",
  },
  {
    id: 2,
    name: "Priya Sharma",
    email: "<EMAIL>",
    phone: "+91 9876543211",
    city: "Delhi",
    status: "Active",
    totalOrders: 3,
    totalSpent: "₹1,797",
    lastOrder: "1/19/2024",
    joinDate: "11/22/2023",
  },
];

const Customers = () => {
  const { toast } = useToast();
  const [customers, setCustomers] = useState(customersData);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [isCustomerViewModalOpen, setIsCustomerViewModalOpen] = useState(false);
  const [isSendEmailModalOpen, setIsSendEmailModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Filter customers based on search and status
  const filteredCustomers = useMemo(() => {
    return customers.filter((customer) => {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        customer.name.toLowerCase().includes(searchLower) ||
        customer.email.toLowerCase().includes(searchLower) ||
        customer.city.toLowerCase().includes(searchLower);
      const matchesStatus =
        statusFilter === "all" ||
        customer.status.toLowerCase() === statusFilter.toLowerCase();

      return matchesSearch && matchesStatus;
    });
  }, [customers, searchTerm, statusFilter]);

  const handleViewCustomer = (customer) => {
    setSelectedCustomer(customer);
    setIsCustomerViewModalOpen(true);
  };

  const handleStatusChange = (customerId, newStatus) => {
    setCustomers((prev) =>
      prev.map((customer) =>
        customer.id === customerId
          ? { ...customer, status: newStatus }
          : customer
      )
    );
    toast({
      title: "Status Updated",
      description: "Customer status has been successfully updated",
    });
  };

  const handleSendNotification = () => {
    setIsSendEmailModalOpen(true);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await new Promise((resolve) => setTimeout(resolve, 1500));
    setIsRefreshing(false);
    toast({
      title: "Customers Refreshed",
      description: "Customer data has been successfully updated",
    });
  };

  const handleExport = async () => {
    setIsExporting(true);
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const csvContent = [
      [
        "Name",
        "Email",
        "Phone",
        "City",
        "Status",
        "Total Orders",
        "Total Spent",
        "Last Order",
        "Join Date",
      ].join(","),
      ...filteredCustomers.map((customer) =>
        [
          `"${customer.name}"`,
          customer.email,
          customer.phone,
          customer.city,
          customer.status,
          customer.totalOrders,
          customer.totalSpent,
          customer.lastOrder,
          customer.joinDate,
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `customers-export-${
      new Date().toISOString().split("T")[0]
    }.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    setIsExporting(false);
    toast({
      title: "Export Successful",
      description: "Customer data has been exported successfully",
    });
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
  };

  return (
    <div className="space-y-4 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Customer Management
          </h1>
          <p className="text-base transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
            Manage customer relationships and communications
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white text-base px-6 py-3"
            onClick={handleSendNotification}
          >
            Send Notification
          </Button>
          <Button
            variant="outline"
            onClick={handleExport}
            disabled={isExporting}
            className="text-base px-6 py-3"
          >
            <Download className="w-5 h-5 mr-2" />
            {isExporting ? "Exporting..." : "Export"}
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="text-base px-6 py-3"
          >
            <RefreshCw
              className={`w-5 h-5 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
            />
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
      </div>

      {/* Customer Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {customerStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-base font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                  {stat.title}
                </p>
                <AnimatedNumber value={stat.value} className="text-3xl" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <Search className="w-5 h-5" />
                <span className="font-medium text-lg">
                  Search & Filter Customers
                </span>
              </div>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="text-base"
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Customers
                </label>
                <Input
                  placeholder="Search by name, email, or city..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
                />
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-base text-gray-500 dark:text-gray-400">
              Showing {filteredCustomers.length} of {customers.length} customers
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customers Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="transition-colors duration-500 ease-in-out text-gray-900 dark:text-white text-xl">
            Customers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-8 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-base">
              <div>Customer</div>
              <div>Contact</div>
              <div>Location</div>
              <div>Orders</div>
              <div>Total Spent</div>
              <div>Last Order</div>
              <div>Status</div>
              <div>Actions</div>
            </div>

            {/* Table Rows */}
            <div className="space-y-3">
              {filteredCustomers.map((customer, index) => (
                <div
                  key={index}
                  className="grid grid-cols-8 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 items-center transition-colors duration-500 ease-in-out"
                >
                  <div>
                    <AnimatedText className="font-medium text-base">
                      {customer.name}
                    </AnimatedText>
                    <div className="text-sm transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                      Joined: {customer.joinDate}
                    </div>
                  </div>
                  <div>
                    <div className="text-base transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
                      {customer.email}
                    </div>
                    <div className="text-sm transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                      {customer.phone}
                    </div>
                  </div>
                  <div className="text-base transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
                    {customer.city}
                  </div>
                  <div className="text-base font-medium transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
                    {customer.totalOrders}
                  </div>
                  <div className="text-base font-medium transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
                    {customer.totalSpent}
                  </div>
                  <div className="text-base transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
                    {customer.lastOrder}
                  </div>
                  <div>
                    <Select
                      value={customer.status}
                      onValueChange={(value) =>
                        handleStatusChange(customer.id, value)
                      }
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Active">
                          <Badge className="bg-green-100 text-green-700">
                            Active
                          </Badge>
                        </SelectItem>
                        <SelectItem value="Inactive">
                          <Badge className="bg-red-100 text-red-700">
                            Inactive
                          </Badge>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewCustomer(customer)}
                      className="p-2"
                    >
                      <Eye className="w-5 h-5" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <CustomerViewModal
        isOpen={isCustomerViewModalOpen}
        onClose={() => setIsCustomerViewModalOpen(false)}
        customer={selectedCustomer}
      />

      <SendEmailModal
        isOpen={isSendEmailModalOpen}
        onClose={() => setIsSendEmailModalOpen(false)}
        customers={customers}
      />
    </div>
  );
};

export default Customers;
