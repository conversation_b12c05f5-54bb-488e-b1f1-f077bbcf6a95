
import React, { useState } from 'react';
import { Tab } from '@headlessui/react';
import GeneralTab from './settings/GeneralTab';
import ProfileTab from './settings/ProfileTab';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

const SettingsProfile = () => {
  const tabs = ['General', 'Profile'];

  return (
    <div className="pt-4 space-y-6 transition-colors duration-500 ease-in-out">
      <div>
        <h1 className="text-2xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
          Settings
        </h1>
        <p className="transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
          Manage your account settings and preferences
        </p>
      </div>

      <div className="w-full">
        <Tab.Group>
          <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1 mb-6">
            {tabs.map((tab) => (
              <Tab
                key={tab}
                className={({ selected }) =>
                  classNames(
                    'w-full rounded-lg py-2.5 text-sm font-medium leading-5 transition-all duration-300 ease-in-out',
                    'ring-white/60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                    selected
                      ? 'bg-white text-blue-700 shadow dark:bg-gray-700 dark:text-blue-300'
                      : 'text-blue-100 hover:bg-white/[0.12] hover:text-white dark:text-gray-300 dark:hover:bg-gray-700/50'
                  )
                }
              >
                {tab}
              </Tab>
            ))}
          </Tab.List>
          <Tab.Panels>
            <Tab.Panel
              className={classNames(
                'rounded-xl bg-white dark:bg-gray-800 p-3 transition-all duration-300 ease-in-out',
                'ring-white/60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2'
              )}
            >
              <GeneralTab />
            </Tab.Panel>
            <Tab.Panel
              className={classNames(
                'rounded-xl bg-white dark:bg-gray-800 p-3 transition-all duration-300 ease-in-out',
                'ring-white/60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2'
              )}
            >
              <ProfileTab />
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      </div>
    </div>
  );
};

export default SettingsProfile;
