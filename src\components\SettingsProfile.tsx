import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";

const SettingsProfile = () => {
  const { toast } = useToast();
  const [settings, setSettings] = useState({
    // General Settings
    defaultDepartment: "Operations",

    // Notification Preferences
    emailNotifications: true,
    smsNotifications: false,
    inAppAlerts: true,

    // Access Roles
    defaultRole: "Admin",

    // Export Defaults
    exportFormat: "Excel",

    // Workflow Toggles
    autoAssignOrders: true,
    enableMarketingBanners: false,
  });

  const handleSaveChanges = () => {
    toast({
      title: "Settings Saved",
      description: "Your settings have been successfully updated.",
    });
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div>
        <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
          Settings
        </h1>
        <p className="text-base transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
          Configure your application preferences and workflow settings
        </p>
      </div>

      {/* General Settings */}
      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">General</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label
              htmlFor="defaultDepartment"
              className="text-base font-medium"
            >
              Default Department
            </Label>
            <Select
              value={settings.defaultDepartment}
              onValueChange={(value) =>
                setSettings((prev) => ({ ...prev, defaultDepartment: value }))
              }
            >
              <SelectTrigger className="text-base px-4 py-2">
                <SelectValue placeholder="Select default department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Operations">Operations</SelectItem>
                <SelectItem value="Customer Service">
                  Customer Service
                </SelectItem>
                <SelectItem value="Sales">Sales</SelectItem>
                <SelectItem value="Marketing">Marketing</SelectItem>
                <SelectItem value="IT">IT</SelectItem>
                <SelectItem value="HR">HR</SelectItem>
                <SelectItem value="Finance">Finance</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Notification Preferences */}
      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">Notification Preferences</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">
                Email Notifications
              </Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Receive notifications via email
              </p>
            </div>
            <Switch
              checked={settings.emailNotifications}
              onCheckedChange={(checked) =>
                setSettings((prev) => ({
                  ...prev,
                  emailNotifications: checked,
                }))
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">SMS Notifications</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Receive notifications via SMS
              </p>
            </div>
            <Switch
              checked={settings.smsNotifications}
              onCheckedChange={(checked) =>
                setSettings((prev) => ({ ...prev, smsNotifications: checked }))
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">In-app Alerts</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Show notifications within the application
              </p>
            </div>
            <Switch
              checked={settings.inAppAlerts}
              onCheckedChange={(checked) =>
                setSettings((prev) => ({ ...prev, inAppAlerts: checked }))
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Access Roles */}
      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">Access Roles</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="defaultRole" className="text-base font-medium">
              Default Role
            </Label>
            <Select
              value={settings.defaultRole}
              onValueChange={(value) =>
                setSettings((prev) => ({ ...prev, defaultRole: value }))
              }
            >
              <SelectTrigger className="text-base px-4 py-2">
                <SelectValue placeholder="Select default role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Super Admin">Super Admin</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
                <SelectItem value="Manager">Manager</SelectItem>
                <SelectItem value="Employee">Employee</SelectItem>
                <SelectItem value="Viewer">Viewer</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Export Defaults */}
      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">Export Defaults</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="exportFormat" className="text-base font-medium">
              Export Format
            </Label>
            <Select
              value={settings.exportFormat}
              onValueChange={(value) =>
                setSettings((prev) => ({ ...prev, exportFormat: value }))
              }
            >
              <SelectTrigger className="text-base px-4 py-2">
                <SelectValue placeholder="Select export format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Excel">Excel (.xlsx)</SelectItem>
                <SelectItem value="CSV">CSV (.csv)</SelectItem>
                <SelectItem value="PDF">PDF (.pdf)</SelectItem>
                <SelectItem value="Word">Word (.docx)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Workflow Toggles */}
      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">Workflow Toggles</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">
                Auto-Assign Orders
              </Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Automatically assign new orders to available staff
              </p>
            </div>
            <Switch
              checked={settings.autoAssignOrders}
              onCheckedChange={(checked) =>
                setSettings((prev) => ({ ...prev, autoAssignOrders: checked }))
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">
                Enable Marketing Banners
              </Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Show promotional banners in the dashboard
              </p>
            </div>
            <Switch
              checked={settings.enableMarketingBanners}
              onCheckedChange={(checked) =>
                setSettings((prev) => ({
                  ...prev,
                  enableMarketingBanners: checked,
                }))
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSaveChanges}
          className="text-white bg-[#FFD700] hover:bg-[#E6C200] transition-all duration-300 ease-in-out text-base px-8 py-4"
        >
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default SettingsProfile;
