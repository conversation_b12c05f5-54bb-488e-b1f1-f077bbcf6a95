
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Camera } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";

const ProfilePage = () => {
  const { toast } = useToast();
  const [profileData, setProfileData] = useState({
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+91 9876543210",
    jobTitle: "Super Admin",
    department: "Operations",
    profileImage: ""
  });

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileData(prev => ({
          ...prev,
          profileImage: e.target?.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUpdateProfile = () => {
    toast({
      title: "Profile Updated",
      description: "Your profile has been successfully updated.",
    });
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
          Profile
        </h1>
        <p className="text-base text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
          Manage your profile information and account settings
        </p>
      </div>

      {/* Profile Section */}
      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">Profile Information</CardTitle>
        </CardHeader>
        <CardContent className="pt-8">
          <div className="flex items-center gap-8 mb-8">
            <div className="relative">
              {profileData.profileImage ? (
                <img 
                  src={profileData.profileImage} 
                  alt="Profile" 
                  className="w-32 h-32 rounded-full object-cover border-4 border-gray-200 dark:border-gray-600"
                />
              ) : (
                <div className="w-32 h-32 rounded-full bg-gradient-to-br from-orange-400 to-pink-400 flex items-center justify-center border-4 border-gray-200 dark:border-gray-600">
                  <span className="text-3xl font-bold text-white">
                    {getInitials(profileData.name)}
                  </span>
                </div>
              )}
              <label className="absolute bottom-0 right-0 bg-white dark:bg-gray-700 rounded-full p-3 shadow-lg cursor-pointer border border-gray-200 dark:border-gray-600 transition-all duration-300 ease-in-out hover:scale-105">
                <Camera className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                <input 
                  type="file" 
                  accept="image/*" 
                  onChange={handleImageUpload}
                  className="hidden" 
                />
              </label>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white">{profileData.name}</h2>
              <p className="text-xl text-gray-600 dark:text-gray-400">{profileData.jobTitle}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-base">Name</Label>
              <Input
                id="name"
                value={profileData.name}
                onChange={(e) => setProfileData(prev => ({...prev, name: e.target.value}))}
                className="transition-colors duration-300 ease-in-out text-base px-4 py-2"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-base">Email</Label>
              <Input
                id="email"
                type="email"
                value={profileData.email}
                onChange={(e) => setProfileData(prev => ({...prev, email: e.target.value}))}
                className="transition-colors duration-300 ease-in-out text-base px-4 py-2"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-base">Phone Number</Label>
              <Input
                id="phone"
                value={profileData.phone}
                onChange={(e) => setProfileData(prev => ({...prev, phone: e.target.value}))}
                className="transition-colors duration-300 ease-in-out text-base px-4 py-2"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="jobTitle" className="text-base">Job Title</Label>
              <Input
                id="jobTitle"
                value={profileData.jobTitle}
                onChange={(e) => setProfileData(prev => ({...prev, jobTitle: e.target.value}))}
                className="transition-colors duration-300 ease-in-out text-base px-4 py-2"
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="department" className="text-base">Department</Label>
              <Input
                id="department"
                value={profileData.department}
                onChange={(e) => setProfileData(prev => ({...prev, department: e.target.value}))}
                className="transition-colors duration-300 ease-in-out text-base px-4 py-2"
              />
            </div>
          </div>

          <div className="flex justify-end mt-8">
            <Button 
              onClick={handleUpdateProfile}
              className="bg-[#FFD700] hover:bg-[#E6C200] text-black transition-all duration-300 ease-in-out text-base px-8 py-4"
            >
              Update Profile
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Account Settings */}
      <Card className="transition-colors duration-500 ease-in-out">
        <CardHeader>
          <CardTitle className="text-2xl">Account Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-base font-medium text-gray-900 dark:text-white">Change Password</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">Update your account password</p>
            </div>
            <Button variant="outline" className="text-base px-4 py-2">
              Change Password
            </Button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-base font-medium text-gray-900 dark:text-white">Manage Notifications</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">Configure your notification preferences</p>
            </div>
            <Button variant="outline" className="text-base px-4 py-2">
              Manage Notifications
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfilePage;
