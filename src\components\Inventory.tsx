import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Download, RefreshCw, AlertTriangle, Edit } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import AddPurchaseModal from "./AddPurchaseModal";
import BatchHistoryModal from "./BatchHistoryModal";
import InventoryEditModal from "./InventoryEditModal";

const inventoryStats = [
  { title: "Total Items", value: "2", color: "text-gray-900" },
  { title: "Low Stock", value: "1", color: "text-orange-600" },
  { title: "Out of Stock", value: "0", color: "text-red-600" },
  { title: "Total Value", value: "₹40,345", color: "text-blue-600" },
];

const inventoryData = [
  {
    id: 1,
    product: "Kapiva Aloe Vera Juice",
    sku: "KAP-ALO-001",
    category: "Health Drinks",
    currentStock: 150,
    minStock: 20,
    stockLevel: "50% capacity",
    value: "₹37,350",
    price: "₹249",
    lastRestocked: "1/15/2024",
    status: "in stock",
    batches: "2"
  },
  {
    id: 2,
    product: "Ashwagandha Capsules",
    sku: "KAP-ASH-001",
    category: "Supplements",
    currentStock: 5,
    minStock: 20,
    stockLevel: "3% capacity",
    value: "₹2,995",
    price: "₹599",
    lastRestocked: "12/10/2023",
    status: "low stock",
    batches: "1"
  }
];

const Inventory = () => {
  const { toast } = useToast();
  const [inventory, setInventory] = useState(inventoryData);
  const [isAddPurchaseOpen, setIsAddPurchaseOpen] = useState(false);
  const [isBatchHistoryOpen, setIsBatchHistoryOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [editingProduct, setEditingProduct] = useState(null);

  const handleRefresh = async () => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    toast({
      title: "Success",
      description: "Inventory List Updated",
    });
  };

  const handleExportReport = () => {
    const csvContent = [
      ['Product', 'SKU', 'Category', 'Current Stock', 'Min Stock', 'Value', 'Price', 'Last Restocked', 'Status'].join(','),
      ...inventory.map(item => [
        item.product,
        item.sku,
        item.category,
        item.currentStock,
        item.minStock,
        item.value,
        item.price,
        item.lastRestocked,
        item.status
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inventory-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);

    toast({
      title: "Success",
      description: "Inventory report downloaded successfully",
    });
  };

  const handleAddPurchase = (purchaseData: any) => {
    console.log('Purchase data:', purchaseData);
    toast({
      title: "Purchase Added",
      description: "Purchase entry has been added successfully",
    });
  };

  const handleViewBatches = (productName: string) => {
    setSelectedProduct(productName);
    setIsBatchHistoryOpen(true);
  };

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setIsEditModalOpen(true);
  };

  const handleUpdateProduct = (updatedProduct: any) => {
    setInventory(prev => prev.map(item => 
      item.id === updatedProduct.id ? { ...item, ...updatedProduct } : item
    ));
    toast({
      title: "Product Updated",
      description: "Product details have been successfully updated",
    });
  };

  const handleStatusChange = (productId, newStatus) => {
    setInventory(prev => prev.map(item => 
      item.id === productId ? { ...item, status: newStatus } : item
    ));
    toast({
      title: "Status Updated",
      description: "Product status has been successfully updated",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">Inventory Management</h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">Monitor stock levels, batches, and purchase entries</p>
        </div>
        <div className="flex gap-3">
          <Button 
            className="bg-green-600 hover:bg-green-700 text-white text-lg px-8 py-4"
            onClick={() => setIsAddPurchaseOpen(true)}
          >
            <Plus className="w-6 h-6 mr-2" />
            Add Purchase
          </Button>
          <Button 
            variant="outline" 
            onClick={handleExportReport}
            className="text-lg px-8 py-4"
          >
            <Download className="w-6 h-6 mr-2" />
            Export
          </Button>
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            className="text-lg px-8 py-4"
          >
            <RefreshCw className="w-6 h-6 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Inventory Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {inventoryStats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400">{stat.title}</p>
                <p className={`text-4xl font-bold ${stat.color}`}>{stat.value}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Low Stock Alert */}
      <Card className="border-orange-200 bg-orange-50 dark:bg-orange-900/20">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <AlertTriangle className="w-8 h-8 text-orange-600" />
            <div className="flex-1">
              <h3 className="font-medium text-orange-800 dark:text-orange-200 text-xl">Low Stock Alert</h3>
              <p className="text-lg text-orange-700 dark:text-orange-300">1 item(s) need restocking</p>
            </div>
          </div>
          <div className="mt-4 p-4 bg-white dark:bg-gray-800 rounded border border-orange-200">
            <p className="text-lg font-medium text-orange-800 dark:text-orange-200">Ashwagandha Capsules (5 left)</p>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
              <span className="font-medium text-xl">🔍 Filters</span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                <Input placeholder="Search products..." className="text-lg" />
              </div>
              <div>
                <label className="block text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                <Select>
                  <SelectTrigger className="text-lg">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="in-stock">In Stock</SelectItem>
                    <SelectItem value="low-stock">Low Stock</SelectItem>
                    <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                <Select>
                  <SelectTrigger className="text-lg">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="health-drinks">Health Drinks</SelectItem>
                    <SelectItem value="supplements">Supplements</SelectItem>
                    <SelectItem value="combo-packs">Combo Packs</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="flex justify-end">
              <Button variant="outline" className="text-lg">Clear Filters</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Table */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-8 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium text-gray-700 dark:text-gray-300 text-lg">
              <div>Product</div>
              <div>Current Stock</div>
              <div>Stock Level</div>
              <div>Value</div>
              <div>Last Restocked</div>
              <div>Status</div>
              <div>Batches</div>
              <div>Actions</div>
            </div>

            {/* Table Rows */}
            <div className="space-y-3">
              {inventory.map((item, index) => (
                <div key={index} className="grid grid-cols-8 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 items-center">
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white text-lg">{item.product}</div>
                    <div className="text-base text-gray-500 dark:text-gray-400">SKU: {item.sku}</div>
                    <div className="text-base text-gray-500 dark:text-gray-400">{item.category}</div>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white text-xl">{item.currentStock}</div>
                    <div className="text-base text-gray-500 dark:text-gray-400">Min: {item.minStock}</div>
                  </div>
                  <div>
                    <div className={`text-lg font-medium ${item.status === 'low stock' ? 'text-red-600' : 'text-green-600'}`}>
                      {item.stockLevel}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white text-xl">{item.value}</div>
                    <div className="text-base text-gray-500 dark:text-gray-400">@ {item.price}</div>
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-lg">{item.lastRestocked}</div>
                  <div>
                    <Select value={item.status} onValueChange={(value) => handleStatusChange(item.id, value)}>
                      <SelectTrigger className="w-full text-lg">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="in stock">
                          <Badge className="bg-green-100 text-green-700 text-base">In Stock</Badge>
                        </SelectItem>
                        <SelectItem value="low stock">
                          <Badge className="bg-red-100 text-red-700 text-base">Low Stock</Badge>
                        </SelectItem>
                        <SelectItem value="out of stock">
                          <Badge className="bg-gray-100 text-gray-700 text-base">Out of Stock</Badge>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewBatches(item.product)}
                      className="text-blue-600 hover:text-blue-700 text-lg"
                    >
                      Batches ({item.batches})
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditProduct(item)}
                      className="p-3"
                    >
                      <Edit className="w-6 h-6" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <AddPurchaseModal
        isOpen={isAddPurchaseOpen}
        onClose={() => setIsAddPurchaseOpen(false)}
        onSubmit={handleAddPurchase}
      />

      <BatchHistoryModal
        isOpen={isBatchHistoryOpen}
        onClose={() => setIsBatchHistoryOpen(false)}
        productName={selectedProduct}
      />

      <InventoryEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        product={editingProduct}
        onUpdate={handleUpdateProduct}
      />
    </div>
  );
};

export default Inventory;
