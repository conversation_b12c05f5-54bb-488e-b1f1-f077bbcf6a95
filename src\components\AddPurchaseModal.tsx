
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Trash2, Plus } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface PurchaseItem {
  id: string;
  product: string;
  quantity: number;
  price: number;
  total: number;
}

interface AddPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

const AddPurchaseModal = ({ isOpen, onClose, onSubmit }: AddPurchaseModalProps) => {
  const [supplier, setSupplier] = useState("");
  const [purchaseDate, setPurchaseDate] = useState("");
  const [items, setItems] = useState<PurchaseItem[]>([
    { id: "1", product: "", quantity: 0, price: 0, total: 0 }
  ]);

  const handleAddItem = () => {
    const newItem: PurchaseItem = {
      id: Date.now().toString(),
      product: "",
      quantity: 0,
      price: 0,
      total: 0
    };
    setItems([...items, newItem]);
  };

  const handleDeleteItem = (id: string) => {
    if (items.length > 1) {
      setItems(items.filter(item => item.id !== id));
    }
  };

  const handleItemChange = (id: string, field: string, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        if (field === 'quantity' || field === 'price') {
          updatedItem.total = updatedItem.quantity * updatedItem.price;
        }
        return updatedItem;
      }
      return item;
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!supplier || !purchaseDate) {
      toast({
        title: "Validation Error",
        description: "Please fill in supplier and purchase date",
        variant: "destructive",
      });
      return;
    }

    const validItems = items.filter(item => item.product && item.quantity > 0 && item.price > 0);
    if (validItems.length === 0) {
      toast({
        title: "Validation Error", 
        description: "Please add at least one valid item",
        variant: "destructive",
      });
      return;
    }

    const purchaseData = {
      supplier,
      purchaseDate,
      items: validItems,
      total: validItems.reduce((sum, item) => sum + item.total, 0)
    };

    onSubmit(purchaseData);
    onClose();
    
    // Reset form
    setSupplier("");
    setPurchaseDate("");
    setItems([{ id: "1", product: "", quantity: 0, price: 0, total: 0 }]);
  };

  const totalAmount = items.reduce((sum, item) => sum + item.total, 0);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Purchase Entry</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="supplier">Supplier *</Label>
              <Select value={supplier} onValueChange={setSupplier}>
                <SelectTrigger>
                  <SelectValue placeholder="Select supplier" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ABC Distributors">ABC Distributors</SelectItem>
                  <SelectItem value="Health Products Ltd">Health Products Ltd</SelectItem>
                  <SelectItem value="Ayurvedic Suppliers">Ayurvedic Suppliers</SelectItem>
                  <SelectItem value="Natural Remedies Co">Natural Remedies Co</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="purchaseDate">Purchase Date *</Label>
              <Input
                id="purchaseDate"
                type="date"
                value={purchaseDate}
                onChange={(e) => setPurchaseDate(e.target.value)}
                required
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Purchase Items</h3>
              <Button type="button" onClick={handleAddItem} size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Add Item
              </Button>
            </div>

            <div className="space-y-3">
              {items.map((item, index) => (
                <div key={item.id} className="grid grid-cols-12 gap-3 items-end p-4 border rounded-lg">
                  <div className="col-span-4">
                    <Label>Product</Label>
                    <Select 
                      value={item.product} 
                      onValueChange={(value) => handleItemChange(item.id, 'product', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select product" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Kapiva Aloe Vera Juice">Kapiva Aloe Vera Juice</SelectItem>
                        <SelectItem value="Ashwagandha Capsules">Ashwagandha Capsules</SelectItem>
                        <SelectItem value="Triphala Churna">Triphala Churna</SelectItem>
                        <SelectItem value="Giloy Juice">Giloy Juice</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="col-span-2">
                    <Label>Quantity</Label>
                    <Input
                      type="number"
                      value={item.quantity || ""}
                      onChange={(e) => handleItemChange(item.id, 'quantity', Number(e.target.value))}
                      placeholder="0"
                      min="0"
                    />
                  </div>
                  
                  <div className="col-span-2">
                    <Label>Price (₹)</Label>
                    <Input
                      type="number"
                      value={item.price || ""}
                      onChange={(e) => handleItemChange(item.id, 'price', Number(e.target.value))}
                      placeholder="0"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  
                  <div className="col-span-3">
                    <Label>Total (₹)</Label>
                    <Input
                      value={`₹${item.total.toFixed(2)}`}
                      readOnly
                      className="bg-gray-50"
                    />
                  </div>
                  
                  <div className="col-span-1">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteItem(item.id)}
                      disabled={items.length === 1}
                    >
                      <Trash2 className="w-4 h-4 text-red-500" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-end p-4 bg-gray-50 rounded-lg">
              <div className="text-right">
                <p className="text-sm text-gray-600">Total Amount</p>
                <p className="text-2xl font-bold text-gray-900">₹{totalAmount.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-green-600 hover:bg-green-700">
              Add Purchase
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddPurchaseModal;
