
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import ThemeToggle from "@/components/ThemeToggle";

// Auth Components
import SignIn from "@/components/auth/SignIn";
import SignUp from "@/components/auth/SignUp";
import ForgotPassword from "@/components/auth/ForgotPassword";
import TwoFactorVerification from "@/components/auth/TwoFactorVerification";

// Main Components
import Dashboard from "@/components/Dashboard";
import Products from "@/components/Products";
import Orders from "@/components/Orders";
import Customers from "@/components/Customers";
import Inventory from "@/components/Inventory";
import Analytics from "@/components/Analytics";
import CouponsReferral from "@/components/CouponsReferral";
import ComplaintsFeedback from "@/components/ComplaintsFeedback";
import Returns from "@/components/Returns";
import StaffManagement from "@/components/StaffManagement";
import Notifications from "@/components/Notifications";
import Calendar from "@/components/Calendar";
import SettingsProfile from "@/components/SettingsProfile";
import ProfilePage from "@/components/ProfilePage";

const queryClient = new QueryClient();

// Header component with notification and user profile
const TopHeader = () => {
  return (
    <div className="fixed top-0 right-0 z-50 flex items-center gap-4 p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="flex items-center gap-4">
        <button className="relative p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full">
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5V7h5v10z" />
          </svg>
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
        </button>
        
        <div className="relative">
          <button className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white font-medium">
            A
          </button>
        </div>
      </div>
    </div>
  );
};

// Main App Layout Component
const AppLayout = ({ children }: { children: React.ReactNode }) => (
  <SidebarProvider>
    <div className="min-h-screen flex w-full bg-gray-50 dark:bg-gray-900 transition-colors duration-500 ease-in-out">
      <AppSidebar />
      <div className="flex-1 flex flex-col transition-colors duration-500 ease-in-out">
        <TopHeader />
        <main className="flex-1 pt-20 p-6 transition-colors duration-500 ease-in-out">
          {children}
        </main>
      </div>
    </div>
    <ThemeToggle />
  </SidebarProvider>
);

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <div className="transition-colors duration-500 ease-in-out">
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Authentication Routes */}
            <Route path="/auth/signin" element={<SignIn />} />
            <Route path="/auth/signup" element={<SignUp />} />
            <Route path="/auth/forgot-password" element={<ForgotPassword />} />
            <Route path="/auth/two-factor" element={<TwoFactorVerification />} />
            
            {/* Main Application Routes */}
            <Route path="/" element={
              <AppLayout>
                <Dashboard />
              </AppLayout>
            } />
            <Route path="/dashboard" element={
              <AppLayout>
                <Dashboard />
              </AppLayout>
            } />
            <Route path="/products" element={
              <AppLayout>
                <Products />
              </AppLayout>
            } />
            <Route path="/orders" element={
              <AppLayout>
                <Orders />
              </AppLayout>
            } />
            <Route path="/customers" element={
              <AppLayout>
                <Customers />
              </AppLayout>
            } />
            <Route path="/inventory" element={
              <AppLayout>
                <Inventory />
              </AppLayout>
            } />
            <Route path="/analytics" element={
              <AppLayout>
                <Analytics />
              </AppLayout>
            } />
            <Route path="/coupons" element={
              <AppLayout>
                <CouponsReferral />
              </AppLayout>
            } />
            <Route path="/feedback" element={
              <AppLayout>
                <ComplaintsFeedback />
              </AppLayout>
            } />
            <Route path="/returns" element={
              <AppLayout>
                <Returns />
              </AppLayout>
            } />
            <Route path="/staff" element={
              <AppLayout>
                <StaffManagement />
              </AppLayout>
            } />
            <Route path="/notifications" element={
              <AppLayout>
                <Notifications />
              </AppLayout>
            } />
            <Route path="/calendar" element={
              <AppLayout>
                <Calendar />
              </AppLayout>
            } />
            <Route path="/settings" element={
              <AppLayout>
                <SettingsProfile />
              </AppLayout>
            } />
            <Route path="/profile" element={
              <AppLayout>
                <ProfilePage />
              </AppLayout>
            } />
          </Routes>
        </BrowserRouter>
      </div>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
