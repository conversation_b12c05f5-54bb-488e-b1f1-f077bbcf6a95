import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  BrowserRouter,
  Routes,
  Route,
  Link,
  useNavigate,
  useLocation,
} from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import ThemeToggle from "@/components/ThemeToggle";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Bell, User, LogOut, Settings } from "lucide-react";
import { useState } from "react";

// Auth Components
import SignIn from "@/components/auth/SignIn";
import SignUp from "@/components/auth/SignUp";
import ForgotPassword from "@/components/auth/ForgotPassword";
import TwoFactorVerification from "@/components/auth/TwoFactorVerification";

// Main Components
import Dashboard from "@/components/Dashboard";
import Products from "@/components/Products";
import Orders from "@/components/Orders";
import Customers from "@/components/Customers";
import Inventory from "@/components/Inventory";
import Analytics from "@/components/Analytics";
import CouponsReferral from "@/components/CouponsReferral";
import ComplaintsFeedback from "@/components/ComplaintsFeedback";
import Returns from "@/components/Returns";
import StaffManagement from "@/components/StaffManagement";
import Notifications from "@/components/Notifications";
import Calendar from "@/components/Calendar";
import SettingsProfile from "@/components/SettingsProfile";
import ProfilePage from "@/components/ProfilePage";

const queryClient = new QueryClient();

// Header component with notification and user profile
const TopHeader = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [profileOpen, setProfileOpen] = useState(false);
  const [previousPath, setPreviousPath] = useState<string>("/");

  const recentActivities = [
    {
      id: 1,
      type: "order",
      message: "New order #ORD-2024-003 received",
      time: "2 min ago",
    },
    {
      id: 2,
      type: "coupon",
      message: "Coupon SAVE20 expires in 2 days",
      time: "1 hour ago",
    },
    {
      id: 3,
      type: "inventory",
      message: "Low stock alert: Ashwagandha Capsules",
      time: "3 hours ago",
    },
    {
      id: 4,
      type: "order",
      message: "Order #ORD-2024-002 shipped",
      time: "5 hours ago",
    },
  ];

  const handleNotificationClick = () => {
    if (location.pathname === "/notifications") {
      // If already on notifications page, go back to previous page
      navigate(previousPath);
    } else {
      // Store current path and navigate to notifications
      setPreviousPath(location.pathname);
      navigate("/notifications");
    }
  };

  const handleSignOut = () => {
    // Handle sign out logic here
    console.log("Signing out...");
  };

  return (
    <div className="fixed top-0 right-0 z-50 flex items-center gap-4 p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="flex items-center gap-4">
        {/* Notification Button */}
        <button
          onClick={handleNotificationClick}
          className="relative p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <Bell className="w-6 h-6" />
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {recentActivities.length}
          </span>
        </button>

        {/* Profile Dropdown */}
        <DropdownMenu open={profileOpen} onOpenChange={setProfileOpen}>
          <DropdownMenuTrigger asChild>
            <button className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white font-medium hover:bg-green-700 transition-colors">
              A
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem asChild>
              <Link
                to="/profile"
                className="flex items-center gap-2 px-4 py-2 cursor-pointer"
              >
                <User className="w-4 h-4" />
                Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link
                to="/settings"
                className="flex items-center gap-2 px-4 py-2 cursor-pointer"
              >
                <Settings className="w-4 h-4" />
                Settings
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleSignOut}
              className="flex items-center gap-2 px-4 py-2 cursor-pointer text-red-600"
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

// Main App Layout Component
const AppLayout = ({ children }: { children: React.ReactNode }) => (
  <SidebarProvider>
    <div className="min-h-screen flex w-full bg-gray-50 dark:bg-gray-900 transition-colors duration-500 ease-in-out">
      <AppSidebar />
      <div className="flex-1 flex flex-col transition-colors duration-500 ease-in-out">
        <TopHeader />
        <main className="flex-1 pt-16 p-6 transition-colors duration-500 ease-in-out">
          {children}
        </main>
      </div>
    </div>
    <ThemeToggle />
  </SidebarProvider>
);

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <div className="transition-colors duration-500 ease-in-out">
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Authentication Routes */}
            <Route path="/auth/signin" element={<SignIn />} />
            <Route path="/auth/signup" element={<SignUp />} />
            <Route path="/auth/forgot-password" element={<ForgotPassword />} />
            <Route
              path="/auth/two-factor"
              element={<TwoFactorVerification />}
            />

            {/* Main Application Routes */}
            <Route
              path="/"
              element={
                <AppLayout>
                  <Dashboard />
                </AppLayout>
              }
            />
            <Route
              path="/dashboard"
              element={
                <AppLayout>
                  <Dashboard />
                </AppLayout>
              }
            />
            <Route
              path="/products"
              element={
                <AppLayout>
                  <Products />
                </AppLayout>
              }
            />
            <Route
              path="/orders"
              element={
                <AppLayout>
                  <Orders />
                </AppLayout>
              }
            />
            <Route
              path="/customers"
              element={
                <AppLayout>
                  <Customers />
                </AppLayout>
              }
            />
            <Route
              path="/inventory"
              element={
                <AppLayout>
                  <Inventory />
                </AppLayout>
              }
            />
            <Route
              path="/analytics"
              element={
                <AppLayout>
                  <Analytics />
                </AppLayout>
              }
            />
            <Route
              path="/coupons"
              element={
                <AppLayout>
                  <CouponsReferral />
                </AppLayout>
              }
            />
            <Route
              path="/feedback"
              element={
                <AppLayout>
                  <ComplaintsFeedback />
                </AppLayout>
              }
            />
            <Route
              path="/returns"
              element={
                <AppLayout>
                  <Returns />
                </AppLayout>
              }
            />
            <Route
              path="/staff"
              element={
                <AppLayout>
                  <StaffManagement />
                </AppLayout>
              }
            />
            <Route
              path="/notifications"
              element={
                <AppLayout>
                  <Notifications />
                </AppLayout>
              }
            />
            <Route
              path="/calendar"
              element={
                <AppLayout>
                  <Calendar />
                </AppLayout>
              }
            />
            <Route
              path="/settings"
              element={
                <AppLayout>
                  <SettingsProfile />
                </AppLayout>
              }
            />
            <Route
              path="/profile"
              element={
                <AppLayout>
                  <ProfilePage />
                </AppLayout>
              }
            />
          </Routes>
        </BrowserRouter>
      </div>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
