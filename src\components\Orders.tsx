import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Eye, Trash2, Search } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import OrderViewModal from "./OrderViewModal";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

const orderStats = [
  { title: "Total Orders", value: "2", color: "text-gray-900" },
  { title: "Pending", value: "0", color: "text-orange-600" },
  { title: "Processing", value: "1", color: "text-blue-600" },
  { title: "Shipped", value: "1", color: "text-purple-600" },
  { title: "Delivered", value: "0", color: "text-green-600" },
];

const ordersData = [
  {
    orderNumber: "ORD-2024-001",
    customer: {
      name: "Rajesh Kumar",
      email: "<EMAIL>",
    },
    date: "1/20/2024",
    items: [
      { name: "Aloe Vera Juice", sku: "KAP-ALO-001", price: 249, quantity: 2 },
      { name: "Amla Juice", sku: "KAP-AML-001", price: 299, quantity: 1 },
    ],
    amount: "₹797",
    status: "processing",
    subtotal: 797,
    shipping: 0,
    tax: 0,
  },
  {
    orderNumber: "ORD-2024-002",
    customer: {
      name: "Priya Sharma",
      email: "<EMAIL>",
    },
    date: "1/19/2024",
    items: [
      { name: "Ashwagandha Capsules", sku: "KAP-ASH-001", price: 599, quantity: 1 },
    ],
    amount: "₹599",
    status: "shipped",
    subtotal: 599,
    shipping: 0,
    tax: 0,
  },
];

const Orders = () => {
  const { toast } = useToast();
  const [orders, setOrders] = useState(ordersData);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [isOrderViewModalOpen, setIsOrderViewModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");

  // Filter orders based on search term and filters
  const filteredOrders = useMemo(() => {
    return orders.filter(order => {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = order.orderNumber.toLowerCase().includes(searchLower) ||
                           order.customer.name.toLowerCase().includes(searchLower);
      const matchesStatus = statusFilter === "all" || order.status.toLowerCase() === statusFilter.toLowerCase();
      // Add category filtering logic if needed
      
      return matchesSearch && matchesStatus;
    });
  }, [orders, searchTerm, statusFilter]);

  const handleDeleteOrder = (index: number) => {
    const orderToDelete = filteredOrders[index];
    const originalIndex = orders.findIndex(o => o.orderNumber === orderToDelete.orderNumber);
    
    setOrders(orders.filter((_, i) => i !== originalIndex));
    toast({
      title: "Order Deleted",
      description: "Order has been removed",
    });
  };

  const handleViewOrder = (order: any) => {
    setSelectedOrder(order);
    setIsOrderViewModalOpen(true);
  };

  const handleStatusChange = (orderNumber: string, newStatus: string) => {
    setOrders(prev => prev.map(order => 
      order.orderNumber === orderNumber ? { ...order, status: newStatus } : order
    ));
    toast({
      title: "Status Updated",
      description: "Order status has been successfully updated",
    });
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setCategoryFilter("all");
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "processing": return "bg-blue-100 text-blue-700";
      case "shipped": return "bg-purple-100 text-purple-700";
      case "delivered": return "bg-green-100 text-green-700";
      case "cancelled": return "bg-red-100 text-red-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Order Management
          </h1>
          <p className="text-lg transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
            Manage customer orders and fulfillment
          </p>
        </div>
      </div>

      {/* Order Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {orderStats.map((stat, index) => (
          <Card key={index} className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                  {stat.title}
                </p>
                <AnimatedNumber value={stat.value} className="text-4xl font-bold" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search & Filter Orders */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <Search className="w-6 h-6" />
                <span className="font-medium text-xl">Search & Filter Orders</span>
              </div>
              <Button 
                variant="outline" 
                onClick={clearFilters}
                className="text-lg px-6 py-3"
              >
                Clear Filters
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Orders
                </label>
                <Input 
                  placeholder="Search by order ID, customer name..." 
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-lg"
                />
              </div>
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-lg">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-lg">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="health-drinks">Health Drinks</SelectItem>
                    <SelectItem value="supplements">Supplements</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="text-lg text-gray-500 dark:text-gray-400">
              Showing {filteredOrders.length} of {orders.length} orders
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="transition-colors duration-500 ease-in-out text-gray-900 dark:text-white text-2xl">
              Orders
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-7 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-lg">
              <div>Order</div>
              <div>Customer</div>
              <div>Date</div>
              <div>Items</div>
              <div>Amount</div>
              <div>Status</div>
              <div>Actions</div>
            </div>

            {/* Table Rows */}
            <div className="space-y-3">
              {filteredOrders.map((order, index) => (
                <div key={index} className="grid grid-cols-7 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 items-center transition-colors duration-500 ease-in-out">
                  <div>
                    <AnimatedText variant="highlight" className="font-medium text-lg">
                      {order.orderNumber}
                    </AnimatedText>
                    <div className="text-base transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                      {order.date}
                    </div>
                  </div>
                  <div>
                    <AnimatedText className="font-medium text-lg">
                      {order.customer.name}
                    </AnimatedText>
                    <div className="text-base transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                      {order.customer.email}
                    </div>
                  </div>
                  <div className="transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300 text-lg">
                    {order.date}
                  </div>
                  <div className="transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300 text-lg">
                    {order.items.length} items
                  </div>
                  <div>
                    <AnimatedText className="font-medium text-lg">
                      {order.amount}
                    </AnimatedText>
                  </div>
                  <div>
                    <Select value={order.status} onValueChange={(value) => handleStatusChange(order.orderNumber, value)}>
                      <SelectTrigger className="w-full text-lg">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="processing">
                          <Badge className="bg-blue-100 text-blue-700 text-base">Processing</Badge>
                        </SelectItem>
                        <SelectItem value="shipped">
                          <Badge className="bg-purple-100 text-purple-700 text-base">Shipped</Badge>
                        </SelectItem>
                        <SelectItem value="delivered">
                          <Badge className="bg-green-100 text-green-700 text-base">Delivered</Badge>
                        </SelectItem>
                        <SelectItem value="cancelled">
                          <Badge className="bg-red-100 text-red-700 text-base">Cancelled</Badge>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewOrder(order)}
                      className="p-3"
                    >
                      <Eye className="w-6 h-6" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteOrder(index)}
                      className="p-3"
                    >
                      <Trash2 className="w-6 h-6" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <OrderViewModal
        isOpen={isOrderViewModalOpen}
        onClose={() => setIsOrderViewModalOpen(false)}
        order={selectedOrder}
      />
    </div>
  );
};

export default Orders;
