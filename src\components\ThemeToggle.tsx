
import React, { useState, useEffect } from 'react';
import { Moon, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

const ThemeToggle = () => {
  const [isDark, setIsDark] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // Check for saved theme preference or default to light mode
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
      setIsDark(true);
      document.documentElement.classList.add('dark');
    }
  }, []);

  const toggleTheme = () => {
    const newTheme = !isDark;
    setIsDark(newTheme);
    
    if (newTheme) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
      toast({
        title: "🌙 Dark Mode Activated",
        description: "Interface switched to dark theme",
        duration: 2000,
      });
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
      toast({
        title: "☀️ Light Mode Activated", 
        description: "Interface switched to light theme",
        duration: 2000,
      });
    }
  };

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={toggleTheme}
      className="fixed bottom-4 right-4 w-12 h-12 rounded-full shadow-lg z-50 bg-white dark:bg-gray-800 border-2 hover:scale-110 transition-all duration-300 ease-in-out hover:shadow-xl"
      title={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
      aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
      role="switch"
      aria-checked={isDark}
    >
      <div className="relative">
        {isDark ? (
          <Sun className="h-5 w-5 text-yellow-500 animate-in spin-in-180 duration-500" />
        ) : (
          <Moon className="h-5 w-5 text-gray-700 animate-in spin-in-180 duration-500" />
        )}
      </div>
    </Button>
  );
};

export default ThemeToggle;
