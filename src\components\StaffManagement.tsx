import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Plus } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";
import AddStaffModal from "./AddStaffModal";

const staffStats = [
  { title: "Total Staff", value: "6", color: "text-gray-900" },
  { title: "Active", value: "5", color: "text-green-600" },
  { title: "Inactive", value: "1", color: "text-red-600" },
  { title: "Departments", value: "4", color: "text-blue-600" },
];

const initialStaffData = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+91 9876543210",
    position: "Operations Manager",
    department: "Operations",
    joinDate: "2023-01-15",
    status: "Active",
  },
  {
    id: 2,
    name: "Priya Patel",
    email: "<EMAIL>",
    phone: "+91 9876543211",
    position: "Customer Service Lead",
    department: "Customer Service",
    joinDate: "2023-03-20",
    status: "Active",
  },
  {
    id: 3,
    name: "Rahul Singh",
    email: "<EMAIL>",
    phone: "+91 9876543212",
    position: "Marketing Executive",
    department: "Marketing",
    joinDate: "2023-05-10",
    status: "Active",
  },
  {
    id: 4,
    name: "Sneha Gupta",
    email: "<EMAIL>",
    phone: "+91 9876543213",
    position: "Quality Analyst",
    department: "Quality",
    joinDate: "2023-07-05",
    status: "Active",
  },
  {
    id: 5,
    name: "Vikram Reddy",
    email: "<EMAIL>",
    phone: "+91 9876543214",
    position: "Inventory Coordinator",
    department: "Operations",
    joinDate: "2023-09-12",
    status: "Active",
  },
  {
    id: 6,
    name: "Anjali Mehta",
    email: "<EMAIL>",
    phone: "+91 9876543215",
    position: "Sales Associate",
    department: "Sales",
    joinDate: "2023-11-01",
    status: "Inactive",
  },
];

const StaffManagement = () => {
  const { toast } = useToast();
  const [staff, setStaff] = useState(initialStaffData);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const handleStatusChange = (id: number, newStatus: string) => {
    setStaff((prev) =>
      prev.map((member) =>
        member.id === id ? { ...member, status: newStatus } : member
      )
    );

    toast({
      title: "Status Updated",
      description: `Staff member status changed to ${newStatus}`,
    });
  };

  const handleAddStaff = (newStaff: any) => {
    setStaff((prev) => [...prev, newStaff]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-700";
      case "Inactive":
        return "bg-red-100 text-red-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  // Update stats based on current staff
  const activeStaff = staff.filter(
    (member) => member.status === "Active"
  ).length;
  const inactiveStaff = staff.filter(
    (member) => member.status === "Inactive"
  ).length;
  const departments = new Set(staff.map((member) => member.department)).size;

  const updatedStats = [
    {
      title: "Total Staff",
      value: staff.length.toString(),
      color: "text-gray-900",
    },
    { title: "Active", value: activeStaff.toString(), color: "text-green-600" },
    {
      title: "Inactive",
      value: inactiveStaff.toString(),
      color: "text-red-600",
    },
    {
      title: "Departments",
      value: departments.toString(),
      color: "text-blue-600",
    },
  ];

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Staff Management
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Manage team members and their access permissions
          </p>
        </div>
        <Button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Staff
        </Button>
      </div>

      {/* Staff Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {updatedStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber value={stat.value} className="text-2xl" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Staff Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Team Members
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Staff Details
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Position
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Department
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Join Date
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Status
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {staff.map((member) => (
                <TableRow key={member.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium">
                        {member.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </div>
                      <div className="flex flex-col">
                        <AnimatedText className="font-medium">
                          {member.name}
                        </AnimatedText>
                        <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                          {member.email}
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                          {member.phone}
                        </span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                      {member.position}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{member.department}</Badge>
                  </TableCell>
                  <TableCell className="text-sm text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                    {member.joinDate}
                  </TableCell>
                  <TableCell>
                    <Select
                      value={member.status}
                      onValueChange={(value) =>
                        handleStatusChange(member.id, value)
                      }
                    >
                      <SelectTrigger className="w-24">
                        <SelectValue>
                          <Badge
                            variant="secondary"
                            className={getStatusColor(member.status)}
                          >
                            {member.status}
                          </Badge>
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Active">Active</SelectItem>
                        <SelectItem value="Inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <AddStaffModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddStaff}
      />
    </div>
  );
};

export default StaffManagement;
