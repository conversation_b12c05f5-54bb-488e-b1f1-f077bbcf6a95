
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 7%;
    --foreground: 210 40% 98%;

    --card: 0 0% 7%;
    --card-foreground: 210 40% 98%;

    --popover: 0 0% 7%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border transition-colors duration-300 ease-in-out;
  }

  body {
    @apply bg-background text-foreground transition-colors duration-500 ease-in-out;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-gray-900 dark:text-white transition-colors duration-500 ease-in-out;
  }

  .table th {
    @apply text-gray-900 dark:text-white font-semibold transition-colors duration-500 ease-in-out;
  }

  .card {
    @apply bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 transition-colors duration-500 ease-in-out;
  }

  .modal {
    @apply bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 transition-colors duration-500 ease-in-out;
  }

  /* Custom animations for theme transitions */
  .theme-transition {
    @apply transition-all duration-500 ease-in-out;
  }

  .spin-in-180 {
    animation: spin-in-180 0.5s ease-in-out;
  }

  @keyframes spin-in-180 {
    from {
      transform: rotate(0deg) scale(0.8);
      opacity: 0;
    }
    to {
      transform: rotate(180deg) scale(1);
      opacity: 1;
    }
  }
}
