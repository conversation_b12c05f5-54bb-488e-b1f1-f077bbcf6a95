import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

const returnStats = [
  { title: "Total Returns", value: "4", color: "text-gray-900" },
  { title: "Pending", value: "2", color: "text-orange-600" },
  { title: "Approved", value: "1", color: "text-green-600" },
  { title: "Refunded", value: "1", color: "text-blue-600" },
];

const returnsData = [
  {
    id: 1,
    orderNumber: "ORD-2024-001",
    customer: "<PERSON><PERSON>",
    email: "<PERSON><PERSON><PERSON>@email.com",
    product: "Ka<PERSON>va <PERSON>ice",
    quantity: 2,
    amount: "₹498",
    reason: "Defective product",
    status: "Pending",
    requestDate: "2024-01-20",
  },
  {
    id: 2,
    orderNumber: "ORD-2024-002",
    customer: "Priya Sharma",
    email: "<EMAIL>",
    product: "Ashwagandha Capsules",
    quantity: 1,
    amount: "₹599",
    reason: "Wrong item received",
    status: "Approved",
    requestDate: "2024-01-19",
  },
  {
    id: 3,
    orderNumber: "ORD-2024-003",
    customer: "Amit Patel",
    email: "<EMAIL>",
    product: "Triphala Churna",
    quantity: 1,
    amount: "₹149",
    reason: "Not satisfied with quality",
    status: "Refunded",
    requestDate: "2024-01-18",
  },
  {
    id: 4,
    orderNumber: "ORD-2024-004",
    customer: "Sneha Reddy",
    email: "<EMAIL>",
    product: "Giloy Juice",
    quantity: 1,
    amount: "₹349",
    reason: "Damaged packaging",
    status: "Pending",
    requestDate: "2024-01-17",
  },
];

const Returns = () => {
  const { toast } = useToast();
  const [returns, setReturns] = useState(returnsData);

  const handleStatusChange = (id: number, newStatus: string) => {
    setReturns((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, status: newStatus } : item
      )
    );

    toast({
      title: "Status Updated",
      description: `Return status changed to ${newStatus}`,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Pending":
        return "bg-orange-100 text-orange-700";
      case "Approved":
        return "bg-green-100 text-green-700";
      case "In Transit":
        return "bg-blue-100 text-blue-700";
      case "Rejected":
        return "bg-red-100 text-red-700";
      case "Refunded":
        return "bg-purple-100 text-purple-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Returns Management
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Handle product returns and refund requests
          </p>
        </div>
      </div>

      {/* Return Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {returnStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={stat.value}
                  className="text-4xl font-bold"
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Returns Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Return Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Order & Customer
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Product
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Amount
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Reason
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Status
                </TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Date
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {returns.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <div className="flex flex-col">
                      <AnimatedText className="font-medium">
                        {item.orderNumber}
                      </AnimatedText>
                      <AnimatedText className="text-sm font-medium">
                        {item.customer}
                      </AnimatedText>
                      <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                        {item.email}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {item.product}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                        Qty: {item.quantity}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                    {item.amount}
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out">
                      {item.reason}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Select
                      value={item.status}
                      onValueChange={(value) =>
                        handleStatusChange(item.id, value)
                      }
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue>
                          <Badge
                            variant="secondary"
                            className={getStatusColor(item.status)}
                          >
                            {item.status}
                          </Badge>
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Pending">Pending</SelectItem>
                        <SelectItem value="Approved">Approved</SelectItem>
                        <SelectItem value="In Transit">In Transit</SelectItem>
                        <SelectItem value="Rejected">Rejected</SelectItem>
                        <SelectItem value="Refunded">Refunded</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell className="text-sm text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                    {item.requestDate}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default Returns;
